So I have the - /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor module and I would you to analyze it and implement tests in the - /Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/executor_tests.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration_tests.rs ( entry point) files. the test will NOT use mocks but real methods/components. the following tests to implement will be - 6.1 Task Error Tests
	•	Test Task Execution Errors: Test handling of errors during task execution
	•	Test Task Timeout Errors: Test handling of task timeouts
	•	Test Task Panic Recovery: Test recovery from task panics
	
	




